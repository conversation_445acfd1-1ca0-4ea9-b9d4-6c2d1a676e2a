export const NGHTTP2_ERR_FRAME_SIZE_ERROR = -522;
export const NGHTTP2_SESSION_SERVER = 0;
export const NGHTTP2_SESSION_CLIENT = 1;
export const NGHTTP2_STREAM_STATE_IDLE = 1;
export const NGHTTP2_STREAM_STATE_OPEN = 2;
export const NGHTTP2_STREAM_STATE_RESERVED_LOCAL = 3;
export const NGHTTP2_STREAM_STATE_RESERVED_REMOTE = 4;
export const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL = 5;
export const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE = 6;
export const NGHTTP2_STREAM_STATE_CLOSED = 7;
export const NGHTTP2_FLAG_NONE = 0;
export const NGHTTP2_FLAG_END_STREAM = 1;
export const NGHTTP2_FLAG_END_HEADERS = 4;
export const NGHTTP2_FLAG_ACK = 1;
export const NGHTTP2_FLAG_PADDED = 8;
export const NGHTTP2_FLAG_PRIORITY = 32;
export const DEFAULT_SETTINGS_HEADER_TABLE_SIZE = 4096;
export const DEFAULT_SETTINGS_ENABLE_PUSH = 1;
export const DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS = 4294967295;
export const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE = 65535;
export const DEFAULT_SETTINGS_MAX_FRAME_SIZE = 16384;
export const DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE = 65535;
export const DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL = 0;
export const MAX_MAX_FRAME_SIZE = 16777215;
export const MIN_MAX_FRAME_SIZE = 16384;
export const MAX_INITIAL_WINDOW_SIZE = 2147483647;
export const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE = 1;
export const NGHTTP2_SETTINGS_ENABLE_PUSH = 2;
export const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS = 3;
export const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE = 4;
export const NGHTTP2_SETTINGS_MAX_FRAME_SIZE = 5;
export const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE = 6;
export const NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL = 8;
export const PADDING_STRATEGY_NONE = 0;
export const PADDING_STRATEGY_ALIGNED = 1;
export const PADDING_STRATEGY_MAX = 2;
export const PADDING_STRATEGY_CALLBACK = 1;
export const NGHTTP2_NO_ERROR = 0;
export const NGHTTP2_PROTOCOL_ERROR = 1;
export const NGHTTP2_INTERNAL_ERROR = 2;
export const NGHTTP2_FLOW_CONTROL_ERROR = 3;
export const NGHTTP2_SETTINGS_TIMEOUT = 4;
export const NGHTTP2_STREAM_CLOSED = 5;
export const NGHTTP2_FRAME_SIZE_ERROR = 6;
export const NGHTTP2_REFUSED_STREAM = 7;
export const NGHTTP2_CANCEL = 8;
export const NGHTTP2_COMPRESSION_ERROR = 9;
export const NGHTTP2_CONNECT_ERROR = 10;
export const NGHTTP2_ENHANCE_YOUR_CALM = 11;
export const NGHTTP2_INADEQUATE_SECURITY = 12;
export const NGHTTP2_HTTP_1_1_REQUIRED = 13;
export const NGHTTP2_DEFAULT_WEIGHT = 16;
export const HTTP2_HEADER_STATUS = ":status";
export const HTTP2_HEADER_METHOD = ":method";
export const HTTP2_HEADER_AUTHORITY = ":authority";
export const HTTP2_HEADER_SCHEME = ":scheme";
export const HTTP2_HEADER_PATH = ":path";
export const HTTP2_HEADER_PROTOCOL = ":protocol";
export const HTTP2_HEADER_ACCEPT_ENCODING = "accept-encoding";
export const HTTP2_HEADER_ACCEPT_LANGUAGE = "accept-language";
export const HTTP2_HEADER_ACCEPT_RANGES = "accept-ranges";
export const HTTP2_HEADER_ACCEPT = "accept";
export const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS = "access-control-allow-credentials";
export const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS = "access-control-allow-headers";
export const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS = "access-control-allow-methods";
export const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN = "access-control-allow-origin";
export const HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS = "access-control-expose-headers";
export const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS = "access-control-request-headers";
export const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD = "access-control-request-method";
export const HTTP2_HEADER_AGE = "age";
export const HTTP2_HEADER_AUTHORIZATION = "authorization";
export const HTTP2_HEADER_CACHE_CONTROL = "cache-control";
export const HTTP2_HEADER_CONNECTION = "connection";
export const HTTP2_HEADER_CONTENT_DISPOSITION = "content-disposition";
export const HTTP2_HEADER_CONTENT_ENCODING = "content-encoding";
export const HTTP2_HEADER_CONTENT_LENGTH = "content-length";
export const HTTP2_HEADER_CONTENT_TYPE = "content-type";
export const HTTP2_HEADER_COOKIE = "cookie";
export const HTTP2_HEADER_DATE = "date";
export const HTTP2_HEADER_ETAG = "etag";
export const HTTP2_HEADER_FORWARDED = "forwarded";
export const HTTP2_HEADER_HOST = "host";
export const HTTP2_HEADER_IF_MODIFIED_SINCE = "if-modified-since";
export const HTTP2_HEADER_IF_NONE_MATCH = "if-none-match";
export const HTTP2_HEADER_IF_RANGE = "if-range";
export const HTTP2_HEADER_LAST_MODIFIED = "last-modified";
export const HTTP2_HEADER_LINK = "link";
export const HTTP2_HEADER_LOCATION = "location";
export const HTTP2_HEADER_RANGE = "range";
export const HTTP2_HEADER_REFERER = "referer";
export const HTTP2_HEADER_SERVER = "server";
export const HTTP2_HEADER_SET_COOKIE = "set-cookie";
export const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY = "strict-transport-security";
export const HTTP2_HEADER_TRANSFER_ENCODING = "transfer-encoding";
export const HTTP2_HEADER_TE = "te";
export const HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS = "upgrade-insecure-requests";
export const HTTP2_HEADER_UPGRADE = "upgrade";
export const HTTP2_HEADER_USER_AGENT = "user-agent";
export const HTTP2_HEADER_VARY = "vary";
export const HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS = "x-content-type-options";
export const HTTP2_HEADER_X_FRAME_OPTIONS = "x-frame-options";
export const HTTP2_HEADER_KEEP_ALIVE = "keep-alive";
export const HTTP2_HEADER_PROXY_CONNECTION = "proxy-connection";
export const HTTP2_HEADER_X_XSS_PROTECTION = "x-xss-protection";
export const HTTP2_HEADER_ALT_SVC = "alt-svc";
export const HTTP2_HEADER_CONTENT_SECURITY_POLICY = "content-security-policy";
export const HTTP2_HEADER_EARLY_DATA = "early-data";
export const HTTP2_HEADER_EXPECT_CT = "expect-ct";
export const HTTP2_HEADER_ORIGIN = "origin";
export const HTTP2_HEADER_PURPOSE = "purpose";
export const HTTP2_HEADER_TIMING_ALLOW_ORIGIN = "timing-allow-origin";
export const HTTP2_HEADER_X_FORWARDED_FOR = "x-forwarded-for";
export const HTTP2_HEADER_PRIORITY = "priority";
export const HTTP2_HEADER_ACCEPT_CHARSET = "accept-charset";
export const HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE = "access-control-max-age";
export const HTTP2_HEADER_ALLOW = "allow";
export const HTTP2_HEADER_CONTENT_LANGUAGE = "content-language";
export const HTTP2_HEADER_CONTENT_LOCATION = "content-location";
export const HTTP2_HEADER_CONTENT_MD5 = "content-md5";
export const HTTP2_HEADER_CONTENT_RANGE = "content-range";
export const HTTP2_HEADER_DNT = "dnt";
export const HTTP2_HEADER_EXPECT = "expect";
export const HTTP2_HEADER_EXPIRES = "expires";
export const HTTP2_HEADER_FROM = "from";
export const HTTP2_HEADER_IF_MATCH = "if-match";
export const HTTP2_HEADER_IF_UNMODIFIED_SINCE = "if-unmodified-since";
export const HTTP2_HEADER_MAX_FORWARDS = "max-forwards";
export const HTTP2_HEADER_PREFER = "prefer";
export const HTTP2_HEADER_PROXY_AUTHENTICATE = "proxy-authenticate";
export const HTTP2_HEADER_PROXY_AUTHORIZATION = "proxy-authorization";
export const HTTP2_HEADER_REFRESH = "refresh";
export const HTTP2_HEADER_RETRY_AFTER = "retry-after";
export const HTTP2_HEADER_TRAILER = "trailer";
export const HTTP2_HEADER_TK = "tk";
export const HTTP2_HEADER_VIA = "via";
export const HTTP2_HEADER_WARNING = "warning";
export const HTTP2_HEADER_WWW_AUTHENTICATE = "www-authenticate";
export const HTTP2_HEADER_HTTP2_SETTINGS = "http2-settings";
export const HTTP2_METHOD_ACL = "ACL";
export const HTTP2_METHOD_BASELINE_CONTROL = "BASELINE-CONTROL";
export const HTTP2_METHOD_BIND = "BIND";
export const HTTP2_METHOD_CHECKIN = "CHECKIN";
export const HTTP2_METHOD_CHECKOUT = "CHECKOUT";
export const HTTP2_METHOD_CONNECT = "CONNECT";
export const HTTP2_METHOD_COPY = "COPY";
export const HTTP2_METHOD_DELETE = "DELETE";
export const HTTP2_METHOD_GET = "GET";
export const HTTP2_METHOD_HEAD = "HEAD";
export const HTTP2_METHOD_LABEL = "LABEL";
export const HTTP2_METHOD_LINK = "LINK";
export const HTTP2_METHOD_LOCK = "LOCK";
export const HTTP2_METHOD_MERGE = "MERGE";
export const HTTP2_METHOD_MKACTIVITY = "MKACTIVITY";
export const HTTP2_METHOD_MKCALENDAR = "MKCALENDAR";
export const HTTP2_METHOD_MKCOL = "MKCOL";
export const HTTP2_METHOD_MKREDIRECTREF = "MKREDIRECTREF";
export const HTTP2_METHOD_MKWORKSPACE = "MKWORKSPACE";
export const HTTP2_METHOD_MOVE = "MOVE";
export const HTTP2_METHOD_OPTIONS = "OPTIONS";
export const HTTP2_METHOD_ORDERPATCH = "ORDERPATCH";
export const HTTP2_METHOD_PATCH = "PATCH";
export const HTTP2_METHOD_POST = "POST";
export const HTTP2_METHOD_PRI = "PRI";
export const HTTP2_METHOD_PROPFIND = "PROPFIND";
export const HTTP2_METHOD_PROPPATCH = "PROPPATCH";
export const HTTP2_METHOD_PUT = "PUT";
export const HTTP2_METHOD_REBIND = "REBIND";
export const HTTP2_METHOD_REPORT = "REPORT";
export const HTTP2_METHOD_SEARCH = "SEARCH";
export const HTTP2_METHOD_TRACE = "TRACE";
export const HTTP2_METHOD_UNBIND = "UNBIND";
export const HTTP2_METHOD_UNCHECKOUT = "UNCHECKOUT";
export const HTTP2_METHOD_UNLINK = "UNLINK";
export const HTTP2_METHOD_UNLOCK = "UNLOCK";
export const HTTP2_METHOD_UPDATE = "UPDATE";
export const HTTP2_METHOD_UPDATEREDIRECTREF = "UPDATEREDIRECTREF";
export const HTTP2_METHOD_VERSION_CONTROL = "VERSION-CONTROL";
export const HTTP_STATUS_CONTINUE = 100;
export const HTTP_STATUS_SWITCHING_PROTOCOLS = 101;
export const HTTP_STATUS_PROCESSING = 102;
export const HTTP_STATUS_EARLY_HINTS = 103;
export const HTTP_STATUS_OK = 200;
export const HTTP_STATUS_CREATED = 201;
export const HTTP_STATUS_ACCEPTED = 202;
export const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION = 203;
export const HTTP_STATUS_NO_CONTENT = 204;
export const HTTP_STATUS_RESET_CONTENT = 205;
export const HTTP_STATUS_PARTIAL_CONTENT = 206;
export const HTTP_STATUS_MULTI_STATUS = 207;
export const HTTP_STATUS_ALREADY_REPORTED = 208;
export const HTTP_STATUS_IM_USED = 226;
export const HTTP_STATUS_MULTIPLE_CHOICES = 300;
export const HTTP_STATUS_MOVED_PERMANENTLY = 301;
export const HTTP_STATUS_FOUND = 302;
export const HTTP_STATUS_SEE_OTHER = 303;
export const HTTP_STATUS_NOT_MODIFIED = 304;
export const HTTP_STATUS_USE_PROXY = 305;
export const HTTP_STATUS_TEMPORARY_REDIRECT = 307;
export const HTTP_STATUS_PERMANENT_REDIRECT = 308;
export const HTTP_STATUS_BAD_REQUEST = 400;
export const HTTP_STATUS_UNAUTHORIZED = 401;
export const HTTP_STATUS_PAYMENT_REQUIRED = 402;
export const HTTP_STATUS_FORBIDDEN = 403;
export const HTTP_STATUS_NOT_FOUND = 404;
export const HTTP_STATUS_METHOD_NOT_ALLOWED = 405;
export const HTTP_STATUS_NOT_ACCEPTABLE = 406;
export const HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED = 407;
export const HTTP_STATUS_REQUEST_TIMEOUT = 408;
export const HTTP_STATUS_CONFLICT = 409;
export const HTTP_STATUS_GONE = 410;
export const HTTP_STATUS_LENGTH_REQUIRED = 411;
export const HTTP_STATUS_PRECONDITION_FAILED = 412;
export const HTTP_STATUS_PAYLOAD_TOO_LARGE = 413;
export const HTTP_STATUS_URI_TOO_LONG = 414;
export const HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415;
export const HTTP_STATUS_RANGE_NOT_SATISFIABLE = 416;
export const HTTP_STATUS_EXPECTATION_FAILED = 417;
export const HTTP_STATUS_TEAPOT = 418;
export const HTTP_STATUS_MISDIRECTED_REQUEST = 421;
export const HTTP_STATUS_UNPROCESSABLE_ENTITY = 422;
export const HTTP_STATUS_LOCKED = 423;
export const HTTP_STATUS_FAILED_DEPENDENCY = 424;
export const HTTP_STATUS_TOO_EARLY = 425;
export const HTTP_STATUS_UPGRADE_REQUIRED = 426;
export const HTTP_STATUS_PRECONDITION_REQUIRED = 428;
export const HTTP_STATUS_TOO_MANY_REQUESTS = 429;
export const HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE = 431;
export const HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS = 451;
export const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;
export const HTTP_STATUS_NOT_IMPLEMENTED = 501;
export const HTTP_STATUS_BAD_GATEWAY = 502;
export const HTTP_STATUS_SERVICE_UNAVAILABLE = 503;
export const HTTP_STATUS_GATEWAY_TIMEOUT = 504;
export const HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED = 505;
export const HTTP_STATUS_VARIANT_ALSO_NEGOTIATES = 506;
export const HTTP_STATUS_INSUFFICIENT_STORAGE = 507;
export const HTTP_STATUS_LOOP_DETECTED = 508;
export const HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED = 509;
export const HTTP_STATUS_NOT_EXTENDED = 510;
export const HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED = 511;
