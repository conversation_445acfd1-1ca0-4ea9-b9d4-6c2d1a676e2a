// RSSHub 代理服务器列表
const proxyList = [
  "https://rsshub.rssforever.com",
  "https://rsshub.app",
  "https://rss.shab.fun",
];

// 简化的请求头 - 只保留必要的头部
const basicHeaders = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Accept': 'application/rss+xml, application/xml, text/xml, */*',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Cache-Control': 'no-cache',
};

// 处理请求的主函数
async function handleRequest(request) {
  try {
    const url = new URL(request.url);

    // 如果是根路径，返回简单的说明页面
    if (url.pathname === '/' || url.pathname === '') {
      return new Response(`
<!DOCTYPE html>
<html>
<head>
    <title>RSSHub Proxy</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>RSSHub Proxy Service</h1>
    <p>这是一个 RSSHub 代理服务</p>
    <p>使用方法：在域名后添加 RSSHub 路径，例如：</p>
    <ul>
        <li><code>/zhihu/hot</code> - 知乎热榜</li>
        <li><code>/bilibili/user/video/2267573</code> - B站用户视频</li>
        <li><code>/weibo/user/1195230310</code> - 微博用户</li>
    </ul>
    <p>当前可用的代理服务器：</p>
    <ul>
        ${proxyList.map(proxy => `<li>${proxy}</li>`).join('')}
    </ul>
</body>
</html>`, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }

    // 尝试所有代理服务器，直到成功或全部失败
    let lastError = null;

    for (const proxy of proxyList) {
      try {
        // 构建目标URL - 将路径和查询参数附加到代理服务器
        const cleanProxy = proxy.endsWith('/') ? proxy : proxy + '/';
        const cleanPath = url.pathname.startsWith('/') ? url.pathname.substring(1) : url.pathname;
        const targetUrl = cleanProxy + cleanPath + url.search;

        console.log(`Trying proxy: ${proxy}`);
        console.log(`Target URL: ${targetUrl}`);

        // 准备请求头 - 使用简化的头部
        const headers = new Headers();

        // 添加基本的请求头
        Object.entries(basicHeaders).forEach(([key, value]) => {
          headers.set(key, value);
        });

        // 保留原始请求中的重要头部
        const importantHeaders = ['authorization', 'cookie'];
        importantHeaders.forEach(headerName => {
          const value = request.headers.get(headerName);
          if (value) {
            headers.set(headerName, value);
          }
        });

        // 创建代理请求
        const proxyRequest = new Request(targetUrl, {
          method: request.method,
          headers: headers,
          body: request.method !== 'GET' && request.method !== 'HEAD' ? request.body : null,
        });

        // 发起请求
        const response = await fetch(proxyRequest);

        console.log(`Response from ${proxy}: ${response.status} ${response.statusText}`);

        // 如果响应成功，返回结果
        if (response.ok) {
          console.log(`Success with proxy: ${proxy}`);

          // 创建响应副本以便读取内容
          const responseClone = response.clone();

          // 构建新的响应头
          const responseHeaders = new Headers();

          // 复制原始响应头
          for (const [key, value] of response.headers) {
            // 跳过一些可能导致问题的头部
            if (!['content-encoding', 'content-length', 'transfer-encoding'].includes(key.toLowerCase())) {
              responseHeaders.set(key, value);
            }
          }

          // 添加CORS和代理信息头部
          responseHeaders.set('Access-Control-Allow-Origin', '*');
          responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
          responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
          responseHeaders.set('X-Proxy-By', 'RSSHub-Cloudflare-Worker');
          responseHeaders.set('X-Proxy-Target', proxy);
          responseHeaders.set('X-Target-URL', targetUrl);

          return new Response(responseClone.body, {
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
          });
        } else {
          // 如果响应不成功，记录错误并尝试下一个代理
          const errorText = await response.text();
          console.error(`Proxy ${proxy} failed with status ${response.status}:`, errorText);
          lastError = {
            proxy,
            status: response.status,
            statusText: response.statusText,
            error: errorText
          };
          continue; // 尝试下一个代理
        }

      } catch (error) {
        console.error(`Error with proxy ${proxy}:`, error);
        lastError = {
          proxy,
          error: error.message
        };
        continue; // 尝试下一个代理
      }
    }

    // 如果所有代理都失败了，返回错误
    return new Response(
      JSON.stringify({
        error: 'All proxy servers failed',
        lastError: lastError,
        triedProxies: proxyList,
        timestamp: new Date().toISOString(),
        url: request.url,
      }, null, 2),
      {
        status: 502,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Proxy error:', error);

    return new Response(
      JSON.stringify({
        error: 'Proxy request failed',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        url: request.url,
      }, null, 2),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// 主事件监听器
addEventListener('fetch', event => {
  const request = event.request;
  
  // 处理OPTIONS请求
  if (request.method === 'OPTIONS') {
    event.respondWith(handleOptions());
  } else {
    event.respondWith(handleRequest(request));
  }
});

// 导出处理函数（用于新的模块语法）
export default {
  async fetch(request) {
    if (request.method === 'OPTIONS') {
      return handleOptions();
    }
    return handleRequest(request);
  },
};
