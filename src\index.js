// RSSHub 代理服务器列表
const proxyList = [
  "https://rss.shab.fun/",
  "https://rsshub.rssforever.com/",
  "https://rsshub.app/",
];

// 处理请求的主函数
async function handleRequest(request) {
  try {
    // 获取当前Worker的URL
    const url = new URL(request.url);
    
    // 随机选择一个代理服务器
    const proxy = proxyList[Math.floor(Math.random() * proxyList.length)];
    
    // 构建目标URL
    const targetUrl = new URL(url.pathname + url.search, proxy);
    
    // 创建新的请求，保持原始请求的方法和头部
    const modifiedRequest = new Request(targetUrl.toString(), {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });
    
    // 发起代理请求
    const response = await fetch(modifiedRequest);
    
    // 创建新的响应，添加CORS头部
    const modifiedResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        // 添加CORS头部，允许跨域访问
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        // 添加代理信息头部
        'X-Proxy-By': 'RSSHub-Cloudflare-Worker',
        'X-Proxy-Target': proxy,
      },
    });
    
    return modifiedResponse;
    
  } catch (error) {
    // 错误处理
    return new Response(
      JSON.stringify({
        error: 'Proxy request failed',
        message: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// 主事件监听器
addEventListener('fetch', event => {
  const request = event.request;
  
  // 处理OPTIONS请求
  if (request.method === 'OPTIONS') {
    event.respondWith(handleOptions());
  } else {
    event.respondWith(handleRequest(request));
  }
});

// 导出处理函数（用于新的模块语法）
export default {
  async fetch(request, env, ctx) {
    if (request.method === 'OPTIONS') {
      return handleOptions();
    }
    return handleRequest(request);
  },
};
