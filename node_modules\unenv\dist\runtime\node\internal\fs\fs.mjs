import { notImplemented, notImplementedAsync } from "../../../_internal/utils.mjs";
import * as fsp from "./promises.mjs";
function callbackify(fn) {
	const fnc = function(...args) {
		const cb = args.pop();
		fn().catch((error) => cb(error)).then((val) => cb(undefined, val));
	};
	fnc.__promisify__ = fn;
	fnc.native = fnc;
	return fnc;
}
export const access = callbackify(fsp.access);
export const appendFile = callbackify(fsp.appendFile);
export const chown = callbackify(fsp.chown);
export const chmod = callbackify(fsp.chmod);
export const copyFile = callbackify(fsp.copyFile);
export const cp = callbackify(fsp.cp);
export const lchown = callbackify(fsp.lchown);
export const lchmod = callbackify(fsp.lchmod);
export const link = callbackify(fsp.link);
export const lstat = callbackify(fsp.lstat);
export const lutimes = callbackify(fsp.lutimes);
export const mkdir = callbackify(fsp.mkdir);
export const mkdtemp = callbackify(fsp.mkdtemp);
export const realpath = callbackify(fsp.realpath);
export const open = callbackify(fsp.open);
export const opendir = callbackify(fsp.opendir);
export const readdir = callbackify(fsp.readdir);
export const readFile = callbackify(fsp.readFile);
export const readlink = callbackify(fsp.readlink);
export const rename = callbackify(fsp.rename);
export const rm = callbackify(fsp.rm);
export const rmdir = callbackify(fsp.rmdir);
export const stat = callbackify(fsp.stat);
export const symlink = callbackify(fsp.symlink);
export const truncate = callbackify(fsp.truncate);
export const unlink = callbackify(fsp.unlink);
export const utimes = callbackify(fsp.utimes);
export const writeFile = callbackify(fsp.writeFile);
export const statfs = callbackify(fsp.statfs);
export const close = /*@__PURE__*/ notImplementedAsync("fs.close");
export const createReadStream = /*@__PURE__*/ notImplementedAsync("fs.createReadStream");
export const createWriteStream = /*@__PURE__*/ notImplementedAsync("fs.createWriteStream");
export const exists = /*@__PURE__*/ notImplementedAsync("fs.exists");
export const fchown = /*@__PURE__*/ notImplementedAsync("fs.fchown");
export const fchmod = /*@__PURE__*/ notImplementedAsync("fs.fchmod");
export const fdatasync = /*@__PURE__*/ notImplementedAsync("fs.fdatasync");
export const fstat = /*@__PURE__*/ notImplementedAsync("fs.fstat");
export const fsync = /*@__PURE__*/ notImplementedAsync("fs.fsync");
export const ftruncate = /*@__PURE__*/ notImplementedAsync("fs.ftruncate");
export const futimes = /*@__PURE__*/ notImplementedAsync("fs.futimes");
export const lstatSync = /*@__PURE__*/ notImplementedAsync("fs.lstatSync");
export const read = /*@__PURE__*/ notImplementedAsync("fs.read");
export const readv = /*@__PURE__*/ notImplementedAsync("fs.readv");
export const realpathSync = /*@__PURE__*/ notImplementedAsync("fs.realpathSync");
export const statSync = /*@__PURE__*/ notImplementedAsync("fs.statSync");
export const unwatchFile = /*@__PURE__*/ notImplementedAsync("fs.unwatchFile");
export const watch = /*@__PURE__*/ notImplementedAsync("fs.watch");
export const watchFile = /*@__PURE__*/ notImplementedAsync("fs.watchFile");
export const write = /*@__PURE__*/ notImplementedAsync("fs.write");
export const writev = /*@__PURE__*/ notImplementedAsync("fs.writev");
export const _toUnixTimestamp = /*@__PURE__*/ notImplementedAsync("fs._toUnixTimestamp");
export const openAsBlob = /*@__PURE__*/ notImplementedAsync("fs.openAsBlob");
export const glob = /*@__PURE__*/ notImplementedAsync("fs.glob");
export const appendFileSync = /*@__PURE__*/ notImplemented("fs.appendFileSync");
export const accessSync = /*@__PURE__*/ notImplemented("fs.accessSync");
export const chownSync = /*@__PURE__*/ notImplemented("fs.chownSync");
export const chmodSync = /*@__PURE__*/ notImplemented("fs.chmodSync");
export const closeSync = /*@__PURE__*/ notImplemented("fs.closeSync");
export const copyFileSync = /*@__PURE__*/ notImplemented("fs.copyFileSync");
export const cpSync = /*@__PURE__*/ notImplemented("fs.cpSync");
export const existsSync = () => false;
export const fchownSync = /*@__PURE__*/ notImplemented("fs.fchownSync");
export const fchmodSync = /*@__PURE__*/ notImplemented("fs.fchmodSync");
export const fdatasyncSync = /*@__PURE__*/ notImplemented("fs.fdatasyncSync");
export const fstatSync = /*@__PURE__*/ notImplemented("fs.fstatSync");
export const fsyncSync = /*@__PURE__*/ notImplemented("fs.fsyncSync");
export const ftruncateSync = /*@__PURE__*/ notImplemented("fs.ftruncateSync");
export const futimesSync = /*@__PURE__*/ notImplemented("fs.futimesSync");
export const lchownSync = /*@__PURE__*/ notImplemented("fs.lchownSync");
export const lchmodSync = /*@__PURE__*/ notImplemented("fs.lchmodSync");
export const linkSync = /*@__PURE__*/ notImplemented("fs.linkSync");
export const lutimesSync = /*@__PURE__*/ notImplemented("fs.lutimesSync");
export const mkdirSync = /*@__PURE__*/ notImplemented("fs.mkdirSync");
export const mkdtempSync = /*@__PURE__*/ notImplemented("fs.mkdtempSync");
export const openSync = /*@__PURE__*/ notImplemented("fs.openSync");
export const opendirSync = /*@__PURE__*/ notImplemented("fs.opendirSync");
export const readdirSync = /*@__PURE__*/ notImplemented("fs.readdirSync");
export const readSync = /*@__PURE__*/ notImplemented("fs.readSync");
export const readvSync = /*@__PURE__*/ notImplemented("fs.readvSync");
export const readFileSync = /*@__PURE__*/ notImplemented("fs.readFileSync");
export const readlinkSync = /*@__PURE__*/ notImplemented("fs.readlinkSync");
export const renameSync = /*@__PURE__*/ notImplemented("fs.renameSync");
export const rmSync = /*@__PURE__*/ notImplemented("fs.rmSync");
export const rmdirSync = /*@__PURE__*/ notImplemented("fs.rmdirSync");
export const symlinkSync = /*@__PURE__*/ notImplemented("fs.symlinkSync");
export const truncateSync = /*@__PURE__*/ notImplemented("fs.truncateSync");
export const unlinkSync = /*@__PURE__*/ notImplemented("fs.unlinkSync");
export const utimesSync = /*@__PURE__*/ notImplemented("fs.utimesSync");
export const writeFileSync = /*@__PURE__*/ notImplemented("fs.writeFileSync");
export const writeSync = /*@__PURE__*/ notImplemented("fs.writeSync");
export const writevSync = /*@__PURE__*/ notImplemented("fs.writevSync");
export const statfsSync = /*@__PURE__*/ notImplemented("fs.statfsSync");
export const globSync = /*@__PURE__*/ notImplemented("fs.globSync");
