# RSSHub Cloudflare Worker 代理

这是一个基于 Cloudflare Workers 的 RSSHub 反向代理服务，可以帮助你访问多个 RSSHub 实例并提供负载均衡。

## 功能特性

- 🔄 **负载均衡**: 随机选择可用的 RSSHub 服务器
- 🌐 **CORS 支持**: 支持跨域访问
- 🛡️ **错误处理**: 完善的错误处理机制
- ⚡ **高性能**: 基于 Cloudflare Workers 的边缘计算

## 支持的 RSSHub 服务器

- https://rss.shab.fun/
- https://rsshub.rssforever.com/
- https://rsshub.app/

## 部署步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 登录 Cloudflare

```bash
npx wrangler login
```

### 3. 部署到 Cloudflare Workers

```bash
npm run deploy
```

### 4. 本地开发（可选）

```bash
npm run dev
```

## 使用方法

部署成功后，你会得到一个 Cloudflare Workers 的 URL，例如：
`https://rsshub-proxy.your-subdomain.workers.dev`

使用方法很简单，只需要将原来的 RSSHub 域名替换为你的 Worker 域名即可：

**原始链接:**
```
https://rsshub.app/bilibili/user/video/2267573
```

**代理后的链接:**
```
https://rsshub-proxy.your-subdomain.workers.dev/bilibili/user/video/2267573
```

## 配置说明

### 修改代理服务器列表

编辑 `src/index.js` 文件中的 `proxyList` 数组，添加或删除 RSSHub 服务器：

```javascript
const proxyList = [
  "https://rss.shab.fun/",
  "https://rsshub.rssforever.com/",
  "https://rsshub.app/",
  // 添加更多服务器...
];
```

### 自定义域名

如果你想使用自定义域名，可以在 `wrangler.toml` 文件中配置：

```toml
routes = [
  { pattern = "rsshub.yourdomain.com/*", zone_name = "yourdomain.com" }
]
```

## 监控和日志

查看实时日志：

```bash
npm run tail
```

## 注意事项

1. 确保你有 Cloudflare 账户并且已经验证
2. Workers 免费版本有请求限制，如需更多请求可升级到付费版本
3. 建议定期检查代理服务器的可用性

## 许可证

MIT License
